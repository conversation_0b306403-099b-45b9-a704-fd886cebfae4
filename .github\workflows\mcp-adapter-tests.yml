name: MCP Adapter Tests

on:
  push:
    branches: [ main, dev, master, develop ]
    paths:
      - 'ai_models/adapters/mcp_adapter.py'
      - 'ai_models/adapters/adapter_factory.py'
      - 'ai_models/adapters/exceptions.py'
      - 'tests/ai_models/adapters/test_mcp_adapter.py'
      - 'tests/ai_models/test_mcp_import.py'
      - 'tests/test_mcp_top_level_import.py'
      - 'tests/ai_models/adapters/conftest.py'
      - 'tests/ai_models/adapters/pytest.ini'
      - 'run_mcp_tests.py'
      - 'install_mcp_sdk.py'
      - 'scripts/run/run_mcp_tests.py'
      - 'scripts/setup/install_mcp_sdk.py'
      - '.github/workflows/mcp-adapter-tests.yml'
  pull_request:
    branches: [ main, dev, master, develop ]
    paths:
      - 'ai_models/adapters/mcp_adapter.py'
      - 'ai_models/adapters/adapter_factory.py'
      - 'ai_models/adapters/exceptions.py'
      - 'tests/ai_models/adapters/test_mcp_adapter.py'
      - 'tests/ai_models/test_mcp_import.py'
      - 'tests/test_mcp_top_level_import.py'
      - 'tests/ai_models/adapters/conftest.py'
      - 'tests/ai_models/adapters/pytest.ini'
      - 'run_mcp_tests.py'
      - 'install_mcp_sdk.py'
      - 'scripts/run/run_mcp_tests.py'
      - 'scripts/setup/install_mcp_sdk.py'
      - '.github/workflows/mcp-adapter-tests.yml'
  workflow_run:
    workflows: ["Gradual Lint Check"]
    types:
      - completed

jobs:
  test-mcp-adapter:
    name: Test MCP Adapter
    runs-on: ${{ matrix.os }}
    if: ${{ github.event_name != 'workflow_run' || github.event.workflow_run.conclusion == 'success' }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest]
      fail-fast: false
    env:
      PYTHONPATH: ${{ github.workspace }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip setuptools wheel
          # Install core test dependencies
          python -m pip install pytest pytest-cov pytest-xdist pytest-asyncio pyyaml requests

          # Install requirements if they exist
          if [ -f "requirements.txt" ]; then
            echo "Installing dependencies from requirements.txt"
            python -m pip install -r requirements.txt || {
              echo "Warning: Failed to install requirements.txt, continuing with minimal dependencies"
            }
          fi

          # Install the package in development mode
          python -m pip install -e . || {
            echo "Warning: Failed to install package in development mode"
            exit 1
          }

      - name: Setup MCP SDK
        run: |
          # Create mock MCP SDK if installation script is not found
          if [ ! -f "install_mcp_sdk.py" ] && [ ! -f "scripts/setup/install_mcp_sdk.py" ]; then
            echo "Creating mock MCP SDK module..."
            mkdir -p modelcontextprotocol
            cat > modelcontextprotocol/__init__.py << 'EOF'
class Client:
    def __init__(self, endpoint, **kwargs):
        self.endpoint = endpoint
        self.kwargs = kwargs
    def connect(self): pass
    def disconnect(self): pass
    def send_message(self, message):
        return f"Mock response to: {message}"
EOF
            pip install -e .
          else
            # Try to run the installation script
            if [ -f "install_mcp_sdk.py" ]; then
              python install_mcp_sdk.py || {
                echo "Warning: Failed to run install_mcp_sdk.py, using mock module"
                mkdir -p modelcontextprotocol
                cat > modelcontextprotocol/__init__.py << 'EOF'
class Client:
    def __init__(self, endpoint, **kwargs):
        self.endpoint = endpoint
        self.kwargs = kwargs
    def connect(self): pass
    def disconnect(self): pass
    def send_message(self, message):
        return f"Mock response to: {message}"
EOF
                pip install -e .
              }
            else
              python scripts/setup/install_mcp_sdk.py || {
                echo "Warning: Failed to run install_mcp_sdk.py, using mock module"
                mkdir -p modelcontextprotocol
                cat > modelcontextprotocol/__init__.py << 'EOF'
class Client:
    def __init__(self, endpoint, **kwargs):
        self.endpoint = endpoint
        self.kwargs = kwargs
    def connect(self): pass
    def disconnect(self): pass
    def send_message(self, message):
        return f"Mock response to: {message}"
EOF
                pip install -e .
              }
            fi
          fi

      - name: Run MCP Adapter tests
        run: |
          python -m pytest \
            --cov=. \
            --cov-report=xml \
            --cov-report=term \
            --cov-fail-under=15 \
            tests/ai_models/adapters/test_mcp_adapter.py \
            tests/ai_models/test_mcp_import.py \
            tests/test_mcp_top_level_import.py \
            --no-header \
            --no-summary \
            -k "not test_mcp_server" \
            --confcutdir=tests/ai_models/adapters \
            --noconftest \
            -v \
            --tb=long \
            --durations=10

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: mcp-adapter
          name: codecov-mcp-adapter
