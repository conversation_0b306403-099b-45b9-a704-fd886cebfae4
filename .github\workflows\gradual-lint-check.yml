name: Gradual <PERSON> Check

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
  workflow_run:
    workflows: ["Auto Fix (Linting & CodeQL Issues)"]
    types:
      - completed

permissions:
  contents: write
  pull-requests: write

jobs:
  gradual-lint:
    if: ${{ github.event_name != 'workflow_run' || github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch full history for comparison
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install ruff

    - name: Run critical lint check only
      id: critical_lint
      continue-on-error: true
      run: |
        echo "Running critical lint check on changed files..."
        # Only check for critical errors that could break functionality
        python scripts/gradual_lint_fix.py --mode pr --base-branch main --critical-only

    - name: Auto-fix non-critical issues and commit
      env:
        GH_TOKEN: ${{ secrets.AUTO_FIX_PAT }}
      run: |
        echo "Attempting to auto-fix non-critical linting issues..."

        # Configure git for auto-commits
        git remote set-url origin https://x-access-token:${GH_TOKEN}@github.com/${{ github.repository }}
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action (Gradual Lint)"

        # Run auto-fix on all changed files
        python scripts/gradual_lint_fix.py --mode pr --base-branch main --fix || echo "Some issues couldn't be auto-fixed"

        # Check if there are any changes to commit
        if ! git diff --quiet; then
          echo "Auto-fixes were applied. Committing changes..."
          git add -A
          git commit -m "fix(lint): auto-fix linting issues in PR #${{ github.event.number }}

          - Applied automatic fixes for import sorting, formatting, and minor issues
          - Focused on maintaining code functionality while improving style
          - Generated by gradual-lint-check workflow"

          # Push the changes back to the PR branch
          git push origin HEAD:${{ github.head_ref }}
          echo "Changes pushed to PR branch"
        else
          echo "No auto-fixes were applied"
        fi

    - name: Final critical check
      run: |
        echo "Running final critical check after auto-fixes..."
        if python scripts/gradual_lint_fix.py --mode pr --base-branch main --critical-only; then
          echo "All changed files pass critical linting checks!"
          echo "Non-critical issues may remain but won't block the PR"
        else
          echo "Critical linting issues found that need manual attention"
          echo "Please fix the critical errors listed above before merging"
          exit 1
        fi
